' ========== 简洁样式清理工具 ==========
' 功能：清除所有自定义样式，只保留系统自带的基本样式
' 保留样式：标题1-9、正文、无间隔等系统默认样式
' 对所有段落应用的自定义样式，将其改为对应的标准样式后再删除重复样式，如有在标题1的基础上自定义了标题1其他，则将其改为标题1后再删除标题1其他。
' 如有在标题2的基础上自定义了标题2其他，则将其改为标题2后再删除标题2其他，依次类推。
' 如有在正文的基础上自定义了正文其他，则将其改为正文后再删除正文其他。
' 各级标题1、2、3、4、5、6、7、8、9等都只保留唯一的一个

Sub 简洁样式清理工具()
    Dim doc As Document
    Dim para As Paragraph
    Dim style As style
    Dim styleName As String
    Dim standardStyleName As String
    Dim i As Integer

    ' 获取当前文档
    Set doc = ActiveDocument

    ' 显示进度信息
    Application.ScreenUpdating = False
    StatusBar = "正在清理样式..."

    ' 第一步：将自定义样式替换为标准样式
    For Each para In doc.Paragraphs
        styleName = para.style.NameLocal
        standardStyleName = GetStandardStyleName(styleName)

        ' 如果找到对应的标准样式，则替换
        If standardStyleName <> "" And standardStyleName <> styleName Then
            On Error Resume Next
            para.style = standardStyleName
            On Error GoTo 0
        End If
    Next para

    ' 第二步：删除自定义样式
    For i = doc.Styles.Count To 1 Step -1
        Set style = doc.Styles(i)

        ' 检查是否为可删除的自定义样式
        If IsCustomStyle(style) Then
            On Error Resume Next
            style.Delete
            On Error GoTo 0
        End If
    Next i

    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    StatusBar = "样式清理完成！"

    ' 显示完成消息
    MsgBox "样式清理完成！" & vbCrLf & _
           "已保留系统默认样式，删除了所有自定义样式。", _
           vbInformation, "简洁样式清理工具"
End Sub

' 获取对应的标准样式名称
Function GetStandardStyleName(styleName As String) As String
    Dim standardName As String
    standardName = ""

    ' 标题样式映射
    If InStr(styleName, "标题1") > 0 Or InStr(styleName, "Heading 1") > 0 Then
        standardName = "标题 1"
    ElseIf InStr(styleName, "标题2") > 0 Or InStr(styleName, "Heading 2") > 0 Then
        standardName = "标题 2"
    ElseIf InStr(styleName, "标题3") > 0 Or InStr(styleName, "Heading 3") > 0 Then
        standardName = "标题 3"
    ElseIf InStr(styleName, "标题4") > 0 Or InStr(styleName, "Heading 4") > 0 Then
        standardName = "标题 4"
    ElseIf InStr(styleName, "标题5") > 0 Or InStr(styleName, "Heading 5") > 0 Then
        standardName = "标题 5"
    ElseIf InStr(styleName, "标题6") > 0 Or InStr(styleName, "Heading 6") > 0 Then
        standardName = "标题 6"
    ElseIf InStr(styleName, "标题7") > 0 Or InStr(styleName, "Heading 7") > 0 Then
        standardName = "标题 7"
    ElseIf InStr(styleName, "标题8") > 0 Or InStr(styleName, "Heading 8") > 0 Then
        standardName = "标题 8"
    ElseIf InStr(styleName, "标题9") > 0 Or InStr(styleName, "Heading 9") > 0 Then
        standardName = "标题 9"
    ' 正文样式映射
    ElseIf InStr(styleName, "正文") > 0 Or InStr(styleName, "Normal") > 0 Then
        If styleName <> "正文" And styleName <> "Normal" Then
            standardName = "正文"
        End If
    End If

    GetStandardStyleName = standardName
End Function

' 判断是否为可删除的自定义样式
Function IsCustomStyle(style As style) As Boolean
    Dim styleName As String
    Dim isBuiltIn As Boolean

    styleName = style.NameLocal
    isBuiltIn = style.BuiltIn

    ' 系统内置样式列表（保留的样式）
    Dim preservedStyles As Variant
    preservedStyles = Array( _
        "正文", "Normal", _
        "标题 1", "标题 2", "标题 3", "标题 4", "标题 5", _
        "标题 6", "标题 7", "标题 8", "标题 9", _
        "Heading 1", "Heading 2", "Heading 3", "Heading 4", "Heading 5", _
        "Heading 6", "Heading 7", "Heading 8", "Heading 9", _
        "无间隔", "No Spacing", _
        "页眉", "页脚", "Header", "Footer", _
        "目录 1", "目录 2", "目录 3", "TOC 1", "TOC 2", "TOC 3", _
        "超链接", "Hyperlink", _
        "强调", "Emphasis", _
        "加强", "Strong", _
        "引用", "Quote", _
        "标题", "Title", _
        "副标题", "Subtitle" _
    )

    ' 检查是否在保留列表中
    Dim i As Integer
    For i = 0 To UBound(preservedStyles)
        If styleName = preservedStyles(i) Then
            IsCustomStyle = False
            Exit Function
        End If
    Next i

    ' 如果不是内置样式，则为自定义样式
    If Not isBuiltIn Then
        IsCustomStyle = True
    Else
        ' 即使是内置样式，如果不在保留列表中也可以删除
        IsCustomStyle = True
    End If
End Function

